name: Reusable - Lambda Deploy

on:
  workflow_call:
    inputs:
      function-name:
        required: true
        type: string
        description: 'Lambda function name'
      image-uri:
        required: true
        type: string
        description: 'Docker image URI to deploy'
      aws-region:
        required: true
        type: string
        description: 'AWS region'
      ecr-account-id:
        required: true
        type: string
        description: 'ECR account ID for OIDC role'
      target-account-id:
        required: true
        type: string
        description: 'Target account ID for deployment'
      github-oidc-role:
        required: true
        type: string
        description: 'GitHub OIDC role name'
      target-role:
        required: true
        type: string
        description: 'Target role name for deployment'
      environment-name:
        required: true
        type: string
        description: 'Environment name (uat/prod) for logging'
      test-payload:
        required: false
        type: string
        description: 'JSON payload for function testing'
        default: '{"path":"/health","httpMethod":"GET"}'
      test-type:
        required: false
        type: string
        description: 'Type of test to perform: api-gateway or direct-invoke'
        default: 'api-gateway'
      lambda-execution-role:
        required: false
        type: string
        description: 'Lambda execution role ARN for new functions'
        default: 'lambda-execution-role'
    outputs:
      deployment-status:
        description: 'Deployment status'
        value: ${{ jobs.deploy.outputs.status }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    outputs:
      status: ${{ steps.deployment-result.outputs.status }}
    steps:
      - name: Configure AWS OIDC role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ inputs.ecr-account-id }}:role/${{ inputs.github-oidc-role }}
          role-session-name: GitHubActions-OIDC-${{ github.run_id }}
          aws-region: ${{ inputs.aws-region }}

      - name: Assume target account role
        run: |
          echo "🔍 Current identity before target role assumption:"
          aws sts get-caller-identity

          echo "🔄 Assuming target account role..."
          OUTPUT=$(aws sts assume-role --role-arn arn:aws:iam::${{ inputs.target-account-id }}:role/${{ inputs.target-role }} --role-session-name GitHubActions-Target-${{ github.run_id }})

          # Set new credentials
          echo "AWS_ACCESS_KEY_ID=$(echo $OUTPUT | jq -r .Credentials.AccessKeyId)" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=$(echo $OUTPUT | jq -r .Credentials.SecretAccessKey)" >> $GITHUB_ENV
          echo "AWS_SESSION_TOKEN=$(echo $OUTPUT | jq -r .Credentials.SessionToken)" >> $GITHUB_ENV

          echo "✅ Verifying new target account identity:"
          aws sts get-caller-identity

      - name: Deploy Lambda function
        run: |
          FUNCTION_NAME="${{ inputs.function-name }}"
          IMAGE_URI="${{ inputs.image-uri }}"
          
          echo "🚀 Deploying $FUNCTION_NAME with image: $IMAGE_URI"
          
          # Check if function exists, create if it doesn't
          if aws lambda get-function --function-name $FUNCTION_NAME --region ${{ inputs.aws-region }} >/dev/null 2>&1; then
            echo "📝 Function exists, updating code..."
            aws lambda update-function-code \
              --function-name $FUNCTION_NAME \
              --image-uri $IMAGE_URI \
              --region ${{ inputs.aws-region }}
          else
            echo "🆕 Function doesn't exist, creating new function..."
            aws lambda create-function \
              --function-name $FUNCTION_NAME \
              --package-type Image \
              --code ImageUri=$IMAGE_URI \
              --role arn:aws:iam::${{ inputs.target-account-id }}:role/${{ inputs.lambda-execution-role }} \
              --timeout 30 \
              --memory-size 128 \
              --region ${{ inputs.aws-region }}
          fi
          
          # Wait for function to be ready
          echo "⏳ Waiting for function to be ready..."
          aws lambda wait function-active \
            --function-name $FUNCTION_NAME \
            --region ${{ inputs.aws-region }}
          
          echo "✅ ${{ inputs.environment-name }} deployment complete"

      - name: Test Lambda function
        run: |
          FUNCTION_NAME="${{ inputs.function-name }}"
          TEST_TYPE="${{ inputs.test-type }}"

          echo "🧪 Testing ${{ inputs.environment-name }} deployment (test-type: $TEST_TYPE)..."

          if [ "$TEST_TYPE" = "api-gateway" ]; then
            echo "🌐 Performing API Gateway health check test..."
            echo "📋 Test payload: ${{ inputs.test-payload }}"

            # Invoke Lambda with API Gateway event structure
            aws lambda invoke \
              --function-name $FUNCTION_NAME \
              --region ${{ inputs.aws-region }} \
              --payload '${{ inputs.test-payload }}' \
              --cli-binary-format raw-in-base64-out \
              response.json

            echo "📋 API Gateway Response:"
            cat response.json
            echo ""

            # Validate API Gateway response structure
            if jq -e '.statusCode' response.json > /dev/null; then
              STATUS_CODE=$(jq -r '.statusCode' response.json)
              if [ "$STATUS_CODE" = "200" ]; then
                echo "✅ API Gateway health check passed (HTTP $STATUS_CODE)"

                # Check if response contains health status
                if jq -e '.body' response.json > /dev/null; then
                  BODY=$(jq -r '.body' response.json)
                  if echo "$BODY" | jq -e '.status' > /dev/null 2>&1; then
                    HEALTH_STATUS=$(echo "$BODY" | jq -r '.status')
                    echo "🏥 Health status: $HEALTH_STATUS"
                  fi
                fi
              else
                echo "❌ API Gateway health check failed (HTTP $STATUS_CODE)"
                exit 1
              fi
            else
              echo "❌ Invalid API Gateway response format"
              exit 1
            fi
          else
            echo "🔧 Performing direct Lambda invocation test..."
            aws lambda invoke \
              --function-name $FUNCTION_NAME \
              --region ${{ inputs.aws-region }} \
              --payload '${{ inputs.test-payload }}' \
              --cli-binary-format raw-in-base64-out \
              response.json

            echo "📋 Direct invocation response:"
            cat response.json
            echo ""
          fi

          echo "✅ ${{ inputs.environment-name }} test complete"

      - name: Set deployment result
        id: deployment-result
        run: |
          echo "status=success" >> $GITHUB_OUTPUT
          echo "✅ Deployment successful"
