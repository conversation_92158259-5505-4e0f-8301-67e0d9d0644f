#!/usr/bin/env ts-node

/**
 * Local DynamoDB Setup Script
 * 
 * This script creates the required DynamoDB tables for local development.
 * In production, tables are managed by Terraform/IaC.
 * 
 * Usage:
 *   npm run setup:local
 *   or
 *   npx ts-node scripts/setup-local-db.ts
 */

import 'reflect-metadata';
import '../src/utils/crypto-polyfill';
import { DynamoDBClient, CreateTableCommand, DescribeTableCommand, ResourceNotFoundException } from '@aws-sdk/client-dynamodb';
import { SESSION_TABLE_DEFINITION } from '../src/infrastructure/database/schema/SessionTableSchema';
import { ConfigService } from '../src/shared/config/config.service';
import { LoggerService } from '../src/shared/logging/logger.service';

interface SetupConfig {
  tableName: string;
  region: string;
  endpoint?: string;
}

class LocalDynamoDBSetup {
  private dynamoClient: DynamoDBClient;
  private logger: LoggerService;
  private config: SetupConfig;

  constructor() {
    // Initialize configuration first
    const configService = new ConfigService();

    // Initialize logger with config
    this.logger = new LoggerService(configService);

    // Load database configuration
    const dbConfig = configService.getDatabase();
    
    // Handle Docker vs local environment
    let endpoint = dbConfig.endpoint;
    if (endpoint && endpoint.includes('dynamodb-local:8000')) {
      // Running outside Docker, use localhost
      endpoint = 'http://localhost:8000';
    }

    this.config = {
      tableName: dbConfig.tableName,
      region: dbConfig.region,
      endpoint: endpoint
    };

    // Initialize DynamoDB client
    const clientConfig = {
      region: this.config.region,
      ...(this.config.endpoint && {
        endpoint: this.config.endpoint
      })
    };
    
    this.dynamoClient = new DynamoDBClient(clientConfig);
    
    this.logger.info('Local DynamoDB setup initialized', {
      tableName: this.config.tableName,
      region: this.config.region,
      endpoint: this.config.endpoint || 'AWS DynamoDB'
    });
  }

  /**
   * Check if table exists
   */
  async tableExists(): Promise<boolean> {
    try {
      await this.dynamoClient.send(new DescribeTableCommand({
        TableName: this.config.tableName
      }));
      return true;
    } catch (error: any) {
      if (error instanceof ResourceNotFoundException || error.name === 'ResourceNotFoundException') {
        return false;
      }
      throw error;
    }
  }

  /**
   * Create the sessions table
   */
  async createTable(): Promise<void> {
    const tableDefinition = {
      ...SESSION_TABLE_DEFINITION,
      TableName: this.config.tableName
    } as any; // Type assertion to handle readonly arrays

    this.logger.info('Creating DynamoDB table...', {
      tableName: this.config.tableName,
      definition: JSON.stringify(tableDefinition, null, 2)
    });

    try {
      await this.dynamoClient.send(new CreateTableCommand(tableDefinition));
      this.logger.info('Table created successfully', { 
        tableName: this.config.tableName 
      });
    } catch (error: any) {
      this.logger.error('Failed to create table', {
        tableName: this.config.tableName,
        error: error.message,
        errorName: error.name
      });
      throw error;
    }
  }

  /**
   * Wait for table to become active
   */
  async waitForTableActive(): Promise<void> {
    this.logger.info('Waiting for table to become active...', {
      tableName: this.config.tableName
    });

    let attempts = 0;
    const maxAttempts = 30; // 30 seconds max wait
    
    while (attempts < maxAttempts) {
      try {
        const result = await this.dynamoClient.send(new DescribeTableCommand({
          TableName: this.config.tableName
        }));
        
        if (result.Table?.TableStatus === 'ACTIVE') {
          this.logger.info('Table is now active', {
            tableName: this.config.tableName
          });
          return;
        }
        
        this.logger.debug('Table status', {
          tableName: this.config.tableName,
          status: result.Table?.TableStatus,
          attempt: attempts + 1
        });
        
      } catch (error) {
        this.logger.warn('Error checking table status', {
          tableName: this.config.tableName,
          error: (error as Error).message,
          attempt: attempts + 1
        });
      }
      
      attempts++;
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    }
    
    throw new Error(`Table did not become active within ${maxAttempts} seconds`);
  }

  /**
   * Run the complete setup process
   */
  async setup(): Promise<void> {
    try {
      this.logger.info('🚀 Starting local DynamoDB setup...');
      
      // Check if table already exists
      const exists = await this.tableExists();
      
      if (exists) {
        this.logger.info('✅ Table already exists', {
          tableName: this.config.tableName
        });
        return;
      }
      
      // Create table
      await this.createTable();
      
      // Wait for table to become active
      await this.waitForTableActive();
      
      this.logger.info('🎉 Local DynamoDB setup completed successfully!');
      this.logger.info('📋 Setup Summary:', {
        tableName: this.config.tableName,
        region: this.config.region,
        endpoint: this.config.endpoint || 'AWS DynamoDB',
        status: 'ACTIVE'
      });
      
    } catch (error) {
      this.logger.error('❌ Local DynamoDB setup failed', {
        error: (error as Error).message,
        stack: (error as Error).stack
      });
      throw error;
    }
  }

  /**
   * Cleanup - close connections
   */
  async cleanup(): Promise<void> {
    try {
      this.dynamoClient.destroy();
      this.logger.info('Cleanup completed');
    } catch (error) {
      this.logger.warn('Cleanup warning', {
        error: (error as Error).message
      });
    }
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  const setup = new LocalDynamoDBSetup();
  
  try {
    await setup.setup();
    process.exit(0);
  } catch (error) {
    console.error('Setup failed:', error);
    process.exit(1);
  } finally {
    await setup.cleanup();
  }
}

// Handle process signals
process.on('SIGINT', async () => {
  console.log('\n🛑 Setup interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Setup terminated');
  process.exit(1);
});

// Run if this file is executed directly
if (require.main === module) {
  console.log('🔧 Local DynamoDB Setup Script');
  console.log('===============================\n');
  
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
