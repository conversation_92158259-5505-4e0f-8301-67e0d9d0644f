# Environment Configuration Template
# Copy this file to environments/.env.development.local and customize for your environment

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Runtime environment (development, uat, production)
NODE_ENV=development

# Server configuration
PORT=3000
HOST=0.0.0.0

# =============================================================================
# AWS CONFIGURATION
# =============================================================================

# AWS region for all services
AWS_REGION=ap-southeast-1

# AWS Credentials (for local development with DynamoDB Local)
# For UAT/Production: use IAM roles instead of hardcoded credentials
AWS_ACCESS_KEY_ID=fakeMyKeyId
AWS_SECRET_ACCESS_KEY=fakeSecretAccessKey

# DynamoDB configuration
# For development: use local DynamoDB endpoint
# For UAT/Production: leave DYNAMODB_ENDPOINT unset to use AWS DynamoDB
DYNAMODB_ENDPOINT=http://dynamodb-local:8000
DYNAMODB_TABLE_NAME=WhatsAppSessions-dev

# AWS Secrets Manager (UAT/Production only)
# MY_SECRET_ARN=arn:aws:secretsmanager:ap-southeast-1:123456789012:secret:whatsapp-api-secrets

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# JWT configuration
JWT_SECRET=dev-super-secret-key-123-change-in-production
QR_TOKEN_EXPIRY_SEC=300

# CORS configuration
CORS_ORIGINS=*

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: debug, info, warn, error
LOG_LEVEL=debug

# Log format: pretty (development), json (production)
LOG_FORMAT=pretty

# =============================================================================
# WHATSAPP CONFIGURATION
# =============================================================================

# WhatsApp session limits and connection settings
MAX_CONCURRENT_SESSIONS=100
RECONNECT_MAX_ATTEMPTS=5
CONNECTION_TIMEOUT_MS=60000
CONNECTION_MAX_IDLE_TIME_MS=1800000
CONNECTION_MAX_AGE_MS=86400000

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================

# Message rate limits
RATE_LIMIT_MESSAGES_PER_MIN=60
RATE_LIMIT_GLOBAL_MESSAGES_PER_MIN=1000
RATE_LIMIT_QR_GENERATION_PER_5MIN=5
RATE_LIMIT_SESSION_CREATION_PER_HOUR=10

# =============================================================================
# MONITORING & HEALTH CONFIGURATION
# =============================================================================

# Monitoring settings
MONITORING_ALERTS_ENABLED=true
HEALTH_CHECK_INTERVAL_MS=30000
METRICS_COLLECTION_INTERVAL_MS=60000

# Alert thresholds
ALERT_MEMORY_THRESHOLD=85
ALERT_CPU_THRESHOLD=80
ALERT_ERROR_RATE_THRESHOLD=5

# =============================================================================
# CLEANUP CONFIGURATION
# =============================================================================

# Session cleanup intervals and retention
SESSION_CLEANUP_INTERVAL_MS=3600000
EXPIRED_SESSIONS_MAX_AGE_MS=604800000
DISCONNECTED_SESSIONS_MAX_AGE_MS=86400000
ERROR_SESSIONS_MAX_AGE_MS=21600000

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================

# Webhook processing settings
WEBHOOK_MAX_QUEUE_SIZE=10000
WEBHOOK_DEFAULT_TIMEOUT_MS=30000
WEBHOOK_PROCESSING_INTERVAL_MS=5000
WEBHOOK_CONCURRENCY=5

# =============================================================================
# MESSAGE PROCESSING CONFIGURATION
# =============================================================================

# Message cache and processing settings
MESSAGE_CACHE_SIZE=10000

# =============================================================================
# ENCRYPTION CONFIGURATION
# =============================================================================

# Encryption key for auth state (minimum 32 characters)
ENCRYPTION_KEY=dev-encryption-key-32-chars-min-change-in-production

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable development features
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
