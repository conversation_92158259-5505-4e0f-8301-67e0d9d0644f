name: Reusable - ECS Config Update

on:
  workflow_call:
    inputs:
      service-name:
        required: true
        type: string
        description: "ECS service name (key in ecs_services.yaml)"
      image-uri:
        required: true
        type: string
        description: "Docker image URI to update in config"
      environment-name:
        required: true
        type: string
        description: "Environment name (uat/prod) for branch naming"
      config-path:
        required: false
        type: string
        description: "Path to ecs_services.yaml config file"
        default: "infra/iac/core/configs/ecs_services.yaml"
    outputs:
      pr-number:
        description: "Pull request number created"
        value: ${{ jobs.update-config.outputs.pr-number }}
      pr-url:
        description: "Pull request URL"
        value: ${{ jobs.update-config.outputs.pr-url }}

jobs:
  update-config:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    outputs:
      pr-number: ${{ steps.create-pr.outputs.pull-request-number }}
      pr-url: ${{ steps.create-pr.outputs.pull-request-url }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.PAT_TOKEN }}
          fetch-depth: 0

      - name: Setup Git and tools
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"

          # Install yq for YAML processing
          sudo wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64
          sudo chmod +x /usr/local/bin/yq

      - name: Create feature branch
        run: |
          BRANCH_NAME="deploy/${{ inputs.service-name }}-${{ inputs.environment-name }}-$(date +%Y%m%d-%H%M%S)"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
          git checkout -b $BRANCH_NAME

      - name: Update ECS service config
        run: |
          echo "🔄 Updating ${{ inputs.service-name }} image in ${{ inputs.config-path }}"
          echo "📝 Current image:"
          yq eval '.${{ inputs.service-name }}.container_image' ${{ inputs.config-path }}

          echo "🔄 Updating to new image: ${{ inputs.image-uri }}"

          # Use yq to update the container_image for the specific service
          yq eval '.${{ inputs.service-name }}.container_image = "${{ inputs.image-uri }}"' -i ${{ inputs.config-path }}

          echo "✅ Updated config for ${{ inputs.service-name }}:"
          yq eval '.${{ inputs.service-name }}' ${{ inputs.config-path }}

          echo "🔍 Verifying change:"
          echo "New image: $(yq eval '.${{ inputs.service-name }}.container_image' ${{ inputs.config-path }})"

      - name: Commit and push changes
        run: |
          git add ${{ inputs.config-path }}

          if git diff --staged --quiet; then
            echo "⚠️ No changes detected in config file"
            exit 0
          fi

          git commit -m "🚀 Deploy ${{ inputs.service-name }} to ${{ inputs.environment-name }}

          - Service: ${{ inputs.service-name }}
          - Image: ${{ inputs.image-uri }}
          - Environment: ${{ inputs.environment-name }}
          - Updated: $(date -u +'%Y-%m-%d %H:%M:%S UTC')

          Auto-generated by GitHub Actions"

          git push origin $BRANCH_NAME

      - name: Create Pull Request
        id: create-pr
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.PAT_TOKEN }}
          branch: ${{ env.BRANCH_NAME }}
          title: "🚀 Update ${{ inputs.service-name }} image for ${{ inputs.environment-name }} deployment"
          body: |
            ## ECS Service Configuration Update

            **Service:** `${{ inputs.service-name }}`
            **Environment:** `${{ inputs.environment-name }}`
            **New Image:** `${{ inputs.image-uri }}`
            **Config File:** `${{ inputs.config-path }}`

            ### Changes Made
            - Updated `container_image` for `${{ inputs.service-name }}` service
            - New image tag will be deployed to `${{ inputs.environment-name }}`

            ### Deployment Flow
            1. ✅ **Config Updated** - This PR updates the service configuration
            2. 🔄 **Auto-Merge** - PR will be automatically merged
            3. 🚀 **Terraform Deploy** - Merge to main triggers Terraform deployment
            4. � **ECS Update** - Terraform updates ECS service with new image

            > **Note:** This is a configuration-only update. The actual ECS deployment will be handled by Terraform when this PR is merged.

            ---
            *Auto-generated by GitHub Actions CI/CD Pipeline*
          labels: |
            deployment
            ${{ inputs.environment-name }}
            ${{ inputs.service-name }}
          assignees: ${{ github.actor }}

      - name: Auto-merge Pull Request
        if: steps.create-pr.outputs.pull-request-number
        run: |
          echo "🔄 Auto-merging PR #${{ steps.create-pr.outputs.pull-request-number }}"
          gh pr merge ${{ steps.create-pr.outputs.pull-request-number }} --auto --squash --delete-branch
        env:
          GH_TOKEN: ${{ secrets.PAT_TOKEN }}

      - name: Output PR details
        run: |
          echo "✅ Pull Request created successfully!"
          echo "PR Number: ${{ steps.create-pr.outputs.pull-request-number }}"
          echo "PR URL: ${{ steps.create-pr.outputs.pull-request-url }}"
          echo "Branch: ${{ env.BRANCH_NAME }}"
