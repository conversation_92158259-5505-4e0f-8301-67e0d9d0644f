name: App - ECS Python CI/CD

on:
  push:
    branches: [main]
    paths:
      - "sample/ecs-python/**"
  pull_request:
    branches: [main]
    paths:
      - "sample/ecs-python/**"
  workflow_dispatch:

# 🔧 PROJECT CONFIGURATION
# For new ECS Python applications, modify these values:
# 1. app-path: Directory containing your application code
# 2. service-name: Key name in ecs_services.yaml config file
# 3. test-hosts: ALB DNS names for health check links in summary
#
# Note: This workflow uses GitOps approach - it updates ecs_services.yaml
# and lets Terraform handle the actual ECS deployment when merged to main

env:
  # Project-specific settings (modify these for your application)
  APP_PATH: sample/ecs-python
  SERVICE_NAME: ecs-python-sample # Must match key in ecs_services.yaml
  UAT_TEST_HOST: app.uat.anchorsprint.com
  PROD_TEST_HOST: app.anchorsprint.com

jobs:
  # Stage 1: Test and Build Python Application
  test-build:
    uses: ./.github/workflows/reusable-python-test-build.yml
    with:
      lambda-path: sample/ecs-python # Reusing lambda-path parameter for app path
      python-version: "3.11"
      run-tests: true
      run-lint: true
      run-build: false # No build step needed for Python ECS apps

  # Stage 2: Build and Push Docker Image to ECR
  docker-build:
    needs: test-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    uses: ./.github/workflows/reusable-docker-build-push.yml
    permissions:
      id-token: write
      contents: read
    with:
      lambda-path: sample/ecs-python # Reusing lambda-path parameter for app path
      function-name: ecs-python-sample # Reusing function-name parameter for image name
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      uat-account-id: ${{ vars.UAT_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}

  # Stage 3: Update UAT Config (Triggers Terraform Deployment)
  deploy-uat:
    needs: docker-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    uses: ./.github/workflows/reusable-ecs-deploy.yml
    permissions:
      contents: write
      pull-requests: write
    with:
      service-name: ecs-python-sample
      image-uri: ${{ needs.docker-build.outputs.image-uri }}
      environment-name: uat

  # Summary Job
  summary:
    needs: [test-build, docker-build, deploy-uat]
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Workflow Summary
        run: |
          echo "## 🐍 ECS Python CI/CD Pipeline Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📊 Pipeline Results" >> $GITHUB_STEP_SUMMARY
          echo "- **Test & Build**: ${{ needs.test-build.result == 'success' && '✅ Passed' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Docker Build**: ${{ needs.docker-build.result == 'success' && '✅ Passed' || needs.docker-build.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **UAT Config Update**: ${{ needs.deploy-uat.result == 'success' && '✅ Passed' || needs.deploy-uat.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔗 Application Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Application**: ECS Python Sample" >> $GITHUB_STEP_SUMMARY
          echo "- **Path**: \`${{ env.APP_PATH }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Service**: \`${{ env.SERVICE_NAME }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Python Version**: 3.11" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [ "${{ needs.docker-build.result }}" == "success" ]; then
            echo "### 🐳 Docker Image" >> $GITHUB_STEP_SUMMARY
            echo "- **Image URI**: \`${{ needs.docker-build.outputs.image-uri }}\`" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi
          if [ "${{ needs.deploy-uat.result }}" == "success" ]; then
            echo "### 🔄 GitOps Deployment" >> $GITHUB_STEP_SUMMARY
            echo "- **Config Updated**: ✅ ecs_services.yaml updated with new image" >> $GITHUB_STEP_SUMMARY
            echo "- **PR Created**: ${{ needs.deploy-uat.outputs.pr-number && '✅ Auto-merged' || '⏳ In Progress' }}" >> $GITHUB_STEP_SUMMARY
            echo "- **Terraform Deploy**: 🔄 Will trigger when config changes are merged" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 🌐 UAT Environment (After Terraform Deploy)" >> $GITHUB_STEP_SUMMARY
            echo "- **Health Check**: https://${{ env.UAT_TEST_HOST }}/health" >> $GITHUB_STEP_SUMMARY
            echo "- **Python Test**: https://${{ env.UAT_TEST_HOST }}/python-test" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi
          echo "### 🚀 Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "- **Monitor Terraform**: Check Terraform deployment progress for UAT" >> $GITHUB_STEP_SUMMARY
          echo "- **Monitor ECS**: Check ECS service health in AWS Console after Terraform completes" >> $GITHUB_STEP_SUMMARY
          echo "- **Check Logs**: Application logs available in CloudWatch" >> $GITHUB_STEP_SUMMARY
          echo "- **Production Deploy**: Team handles production deployment manually via Terraform" >> $GITHUB_STEP_SUMMARY
