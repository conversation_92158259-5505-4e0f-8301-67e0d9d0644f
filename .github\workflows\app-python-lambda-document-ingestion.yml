name: App - Python Lambda Document Ingestion CI/CD

on:
  push:
    branches: [ main ]
    paths:
      - 'apps/document-ingestion/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/document-ingestion/**'
  workflow_dispatch:

jobs:
  # Test and Build
  test-build:
    uses: ./.github/workflows/reusable-python-test-build.yml
    with:
      lambda-path: apps/document-ingestion
      python-version: '3.12'
      run-tests: true
      run-lint: true
      run-build: true

  # Build and Push Docker Image
  docker-build:
    uses: ./.github/workflows/reusable-docker-build-push.yml
    needs: test-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    permissions:
      id-token: write
      contents: read
    with:
      lambda-path: apps/document-ingestion
      function-name: lambda-document-ingestion
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      uat-account-id: ${{ vars.UAT_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}

  # Deploy to UAT (automatic)
  deploy-uat:
    uses: ./.github/workflows/reusable-lambda-deploy.yml
    needs: docker-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    permissions:
      id-token: write
      contents: read
    with:
      function-name: uat-lambda-document-ingestion
      image-uri: ${{ needs.docker-build.outputs.image-uri }}
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      target-account-id: ${{ vars.UAT_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}
      environment-name: UAT
      test-payload: '{"path":"/health","httpMethod":"GET"}'

  # Deploy to Production (manual trigger only)
  deploy-prod:
    uses: ./.github/workflows/reusable-lambda-deploy.yml
    needs: [docker-build, deploy-uat]
    if: github.ref == 'refs/heads/main' && github.event_name == 'workflow_dispatch'
    permissions:
      id-token: write
      contents: read
    with:
      function-name: prod-lambda-document-ingestion
      image-uri: ${{ needs.docker-build.outputs.image-uri }}
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      target-account-id: ${{ vars.PROD_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}
      environment-name: Production
      test-payload: '{"path":"/health","httpMethod":"GET"}'

  # Deployment Summary
  summary:
    runs-on: ubuntu-latest
    needs: [docker-build, deploy-uat, deploy-prod]
    if: always() && (needs.deploy-uat.result == 'success' || needs.deploy-prod.result == 'success')
    steps:
      - name: Deployment Summary
        run: |
          echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Docker Image**: ${{ needs.docker-build.outputs.image-uri }}" >> $GITHUB_STEP_SUMMARY
          echo "- **UAT**: ${{ needs.deploy-uat.result == 'success' && '✅ Deployed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Production**: ${{ needs.deploy-prod.result == 'success' && '✅ Deployed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY


